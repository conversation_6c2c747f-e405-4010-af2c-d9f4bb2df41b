using Unity.Entities;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using Watermelon.SquadShooter;
using PlayerFAP.Components.Chest;
using Watermelon;

namespace PlayerFAP.Authorings
{
    /// <summary>
    /// Authoring component for creating ECS chest entities
    /// This MonoBehaviour allows designers to configure chest properties in the editor
    /// </summary>
    public class ChestAuthoring : MonoBehaviour
    {
        [Header("Chest Configuration")]
        [SerializeField] private ChestType chestType = ChestType.InGame;
        [SerializeField] private float openDuration = 3f;
        [SerializeField] private float interactionRange = 2f;

        [Header("UI References")]
        [SerializeField] private Transform fillCircleHolder;
        [SerializeField] private Image fillCircleImage;
        [SerializeField] private Button rewardButton; // For rewarded chests
        [SerializeField] private Canvas adCanvas; // For rewarded chests

        [Header("Visual References")]
        [SerializeField] private Animator chestAnimator;
        [SerializeField] private GameObject particleEffect;

        [Header("Reward Configuration")]
        [SerializeField] private int minCurrencyReward = 5;
        [SerializeField] private int maxCurrencyReward = 15;
        [SerializeField] private float weaponCardDropChance = 0.1f;
        [SerializeField] private float healDropChance = 0.05f;
        [SerializeField] private CurrencyType rewardCurrencyType = CurrencyType.Coins;
        [SerializeField] private WeaponSubModuleState preferredWeaponType = WeaponSubModuleState.EmptyHand;

        [Header("Drop Data")]
        [SerializeField] private List<ChestDropData> dropData = new List<ChestDropData>();

        /// <summary>
        /// Property to get/set the chest type
        /// </summary>
        public ChestType ChestType
        {
            get => chestType;
            set => chestType = value;
        }

        /// <summary>
        /// Property to get/set the open duration
        /// </summary>
        public float OpenDuration
        {
            get => openDuration;
            set => openDuration = Mathf.Max(0f, value);
        }

        /// <summary>
        /// Property to get/set the interaction range
        /// </summary>
        public float InteractionRange
        {
            get => interactionRange;
            set => interactionRange = Mathf.Max(0f, value);
        }

        /// <summary>
        /// Get the fill circle image component
        /// </summary>
        public Image FillCircleImage => fillCircleImage;

        /// <summary>
        /// Get the fill circle holder transform
        /// </summary>
        public Transform FillCircleHolder => fillCircleHolder;

        /// <summary>
        /// Get the chest animator component
        /// </summary>
        public Animator ChestAnimator => chestAnimator;

        /// <summary>
        /// Get the particle effect GameObject
        /// </summary>
        public GameObject ParticleEffect => particleEffect;

        /// <summary>
        /// Serializable class for chest drop data configuration
        /// </summary>
        [System.Serializable]
        public class ChestDropData
        {
            public DropableItemType dropType = DropableItemType.Currency;
            public CurrencyType currencyType = CurrencyType.Coins;
            public WeaponSubModuleState weaponType = WeaponSubModuleState.EmptyHand;
            public int amount = 1;
            public float dropChance = 1f;
        }

        /// <summary>
        /// Validate the configuration in the editor
        /// </summary>
        private void OnValidate()
        {
            openDuration = Mathf.Max(0f, openDuration);
            interactionRange = Mathf.Max(0f, interactionRange);
            minCurrencyReward = Mathf.Max(0, minCurrencyReward);
            maxCurrencyReward = Mathf.Max(minCurrencyReward, maxCurrencyReward);
            weaponCardDropChance = Mathf.Clamp01(weaponCardDropChance);
            healDropChance = Mathf.Clamp01(healDropChance);

            // Ensure we have UI components for InGame chests
            if (chestType == ChestType.InGame && (fillCircleHolder == null || fillCircleImage == null))
            {
                Debug.LogWarning($"InGame chest '{name}' requires fillCircleHolder and fillCircleImage references!", this);
            }

            // Ensure we have button for rewarded chests
            if (chestType == ChestType.Rewarded && rewardButton == null)
            {
                Debug.LogWarning($"Rewarded chest '{name}' requires rewardButton reference!", this);
            }
        }

        /// <summary>
        /// Baker class to convert MonoBehaviour data to ECS components
        /// </summary>
        public class ChestBaker : Baker<ChestAuthoring>
        {
            public override void Bake(ChestAuthoring authoring)
            {
                var entity = GetEntity(TransformUsageFlags.Dynamic);

                // Add chest tag
                AddComponent<ChestTag>(entity);

                // Add main chest component
                AddComponent(entity, new ChestComponent
                {
                    ChestType = authoring.chestType,
                    OpenDuration = authoring.openDuration,
                    IsOpened = false,
                    IsPlayerNearby = false,
                    InteractionRange = authoring.interactionRange,
                    UIEntity = Entity.Null
                });

                // Add opening component
                AddComponent(entity, new ChestOpeningComponent
                {
                    StartTime = 0f,
                    Progress = 0f,
                    IsOpening = false,
                    OpeningCancelled = false
                });

                // Add UI component
                AddComponent(entity, new ChestUIComponent
                {
                    FillCircleEntity = Entity.Null,
                    UIVisible = false,
                    UIScaleTarget = 0f,
                    UIScaleSpeed = 5f
                });

                // Add reward component
                AddComponent(entity, new ChestRewardComponent
                {
                    MinCurrencyReward = authoring.minCurrencyReward,
                    MaxCurrencyReward = authoring.maxCurrencyReward,
                    WeaponCardDropChance = authoring.weaponCardDropChance,
                    HealDropChance = authoring.healDropChance,
                    RewardCurrencyType = authoring.rewardCurrencyType,
                    PreferredWeaponType = authoring.preferredWeaponType
                });

                // Add drop data buffer
                var dropBuffer = AddBuffer<ChestDropDataElement>(entity);
                foreach (var drop in authoring.dropData)
                {
                    dropBuffer.Add(new ChestDropDataElement
                    {
                        DropType = drop.dropType,
                        CurrencyType = drop.currencyType,
                        WeaponType = drop.weaponType,
                        Amount = drop.amount,
                        DropChance = drop.dropChance
                    });
                }

                // Add animation component
                AddComponent(entity, new ChestAnimationComponent
                {
                    CurrentAnimationState = 0,
                    TriggerShake = false,
                    TriggerOpen = false,
                    TriggerIdle = true
                });

                // Add particle component
                AddComponent(entity, new ChestParticleComponent
                {
                    ParticleEntity = Entity.Null,
                    ParticleActive = true
                });

                // Add audio component
                AddComponent(entity, new ChestAudioComponent
                {
                    PlayOpenSound = false,
                    PlayShakeSound = false,
                    AudioVolume = 1f
                });

                // Add rewarded chest component if needed
                if (authoring.chestType == ChestType.Rewarded)
                {
                    AddComponent(entity, new RewardedChestComponent
                    {
                        ButtonEntity = Entity.Null,
                        AdWatched = false,
                        ButtonVisible = false
                    });
                }

                // Add reference to the authoring component for UI/Animation systems
                AddComponent(entity, new ChestAuthoringReference
                {
                    AuthoringComponent = new UnityObjectRef<MonoBehaviour> { Value = authoring }
                });
            }
        }
    }
}
