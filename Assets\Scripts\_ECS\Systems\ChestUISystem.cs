using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.UI;
using PlayerFAP.Components.Chest;
using PlayerFAP.Authorings;
using Watermelon;

namespace PlayerFAP.Systems.Chest
{
    /// <summary>
    /// System for handling chest UI updates (MonoBehaviour components)
    /// This system bridges ECS data with Unity UI components
    /// </summary>
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial class ChestUISystem : SystemBase
    {
        private EntityQuery chestQuery;

        protected override void OnCreate()
        {
            // Query for chests with UI components
            chestQuery = GetEntityQuery(
                ComponentType.ReadOnly<ChestTag>(),
                ComponentType.ReadOnly<ChestComponent>(),
                ComponentType.ReadOnly<ChestOpeningComponent>(),
                ComponentType.ReadOnly<ChestUIComponent>()
            );

            RequireForUpdate(chestQuery);
        }

        protected override void OnUpdate()
        {
            var deltaTime = SystemAPI.Time.DeltaTime;

            // Process each chest entity
            Entities
                .WithAll<ChestTag>()
                .ForEach((Entity entity, ref ChestComponent chest, ref ChestOpeningComponent opening, 
                         ref ChestUIComponent ui) =>
                {
                    // Find the corresponding ChestAuthoring component
                    var chestAuthoring = GetChestAuthoring(entity);
                    if (chestAuthoring == null)
                        return;

                    // Update fill circle for InGame chests
                    if (chest.ChestType == ChestType.InGame)
                    {
                        UpdateFillCircleUI(chestAuthoring, opening, ui, deltaTime);
                    }

                    // Update rewarded chest button
                    if (chest.ChestType == ChestType.Rewarded)
                    {
                        UpdateRewardedChestUI(chestAuthoring, chest, ui);
                    }

                    // Update UI scale animation
                    UpdateUIScaleAnimation(chestAuthoring, ui, deltaTime);

                }).WithoutBurst().Run();
        }

        private ChestAuthoring GetChestAuthoring(Entity entity)
        {
            // Try to get the authoring reference component
            if (EntityManager.HasComponent<ChestAuthoringReference>(entity))
            {
                var reference = EntityManager.GetComponentData<ChestAuthoringReference>(entity);
                return reference.AuthoringComponent.Value as ChestAuthoring;
            }

            return null;
        }

        private void UpdateFillCircleUI(ChestAuthoring chestAuthoring, ChestOpeningComponent opening, 
                                       ChestUIComponent ui, float deltaTime)
        {
            if (chestAuthoring.FillCircleImage == null || chestAuthoring.FillCircleHolder == null)
                return;

            // Update fill amount based on opening progress
            chestAuthoring.FillCircleImage.fillAmount = opening.Progress;

            // Update holder scale based on UI visibility
            var targetScale = ui.UIVisible ? Vector3.one : Vector3.zero;
            var currentScale = chestAuthoring.FillCircleHolder.localScale;
            var newScale = Vector3.Lerp(currentScale, targetScale, ui.UIScaleSpeed * deltaTime);
            chestAuthoring.FillCircleHolder.localScale = newScale;
        }

        private void UpdateRewardedChestUI(ChestAuthoring chestAuthoring, ChestComponent chest, ChestUIComponent ui)
        {
            // Handle rewarded chest button visibility
            // This would be implemented based on your specific UI setup
            // For now, this is a placeholder
        }

        private void UpdateUIScaleAnimation(ChestAuthoring chestAuthoring, ChestUIComponent ui, float deltaTime)
        {
            // Handle general UI scale animations
            // This could be used for other UI elements that need scaling
        }
    }



    /// <summary>
    /// System for handling chest animations (Animator components)
    /// </summary>
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial class ChestAnimationSystem : SystemBase
    {
        private EntityQuery chestAnimationQuery;

        // Animation state hashes (matching AbstractChestBehavior)
        private static readonly int IDLE_HASH = Animator.StringToHash("Idle");
        private static readonly int SHAKE_HASH = Animator.StringToHash("Shake");
        private static readonly int OPEN_HASH = Animator.StringToHash("Open");

        protected override void OnCreate()
        {
            chestAnimationQuery = GetEntityQuery(
                ComponentType.ReadWrite<ChestAnimationComponent>(),
                ComponentType.ReadOnly<ChestTag>()
            );

            RequireForUpdate(chestAnimationQuery);
        }

        protected override void OnUpdate()
        {
            Entities
                .WithAll<ChestTag>()
                .ForEach((Entity entity, ref ChestAnimationComponent animation) =>
                {
                    // Find the corresponding Animator component
                    var chestAuthoring = GetChestAuthoring(entity);
                    if (chestAuthoring == null)
                        return;

                    // Get the animator from the authoring component
                    // This would need to be exposed in ChestAuthoring
                    var animator = chestAuthoring.GetComponent<Animator>();
                    if (animator == null)
                        return;

                    // Process animation triggers
                    if (animation.TriggerIdle)
                    {
                        animator.SetTrigger(IDLE_HASH);
                        animation.TriggerIdle = false;
                        animation.CurrentAnimationState = IDLE_HASH;
                    }
                    else if (animation.TriggerShake)
                    {
                        animator.SetTrigger(SHAKE_HASH);
                        animation.TriggerShake = false;
                        animation.CurrentAnimationState = SHAKE_HASH;
                    }
                    else if (animation.TriggerOpen)
                    {
                        animator.SetTrigger(OPEN_HASH);
                        animation.TriggerOpen = false;
                        animation.CurrentAnimationState = OPEN_HASH;
                    }

                }).WithoutBurst().Run();
        }

        private ChestAuthoring GetChestAuthoring(Entity entity)
        {
            // Try to get the authoring reference component
            if (EntityManager.HasComponent<ChestAuthoringReference>(entity))
            {
                var reference = EntityManager.GetComponentData<ChestAuthoringReference>(entity);
                return reference.AuthoringComponent.Value as ChestAuthoring;
            }

            return null;
        }
    }

    /// <summary>
    /// System for handling chest audio
    /// </summary>
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial class ChestAudioSystem : SystemBase
    {
        protected override void OnCreate()
        {
            RequireForUpdate<ChestAudioComponent>();
        }

        protected override void OnUpdate()
        {
            Entities
                .WithAll<ChestTag>()
                .ForEach((Entity entity, ref ChestAudioComponent audio) =>
                {
                    if (audio.PlayOpenSound)
                    {
                        // Play chest open sound using the existing audio system
                        //AudioController.PlaySound(AudioController.Sounds.chestOpen, audio.AudioVolume);
                        audio.PlayOpenSound = false;
                    }

                    if (audio.PlayShakeSound)
                    {
                        // Play chest shake sound if needed
                        // AudioController.PlaySound(AudioController.Sounds.chestShake, audio.AudioVolume);
                        audio.PlayShakeSound = false;
                    }

                }).WithoutBurst().Run();
        }
    }

    /// <summary>
    /// System for handling chest particle effects
    /// </summary>
    [UpdateInGroup(typeof(PresentationSystemGroup))]
    public partial class ChestParticleSystem : SystemBase
    {
        protected override void OnCreate()
        {
            RequireForUpdate<ChestParticleComponent>();
        }

        protected override void OnUpdate()
        {
            Entities
                .WithAll<ChestTag>()
                .ForEach((Entity entity, ref ChestParticleComponent particle) =>
                {
                    var chestAuthoring = GetChestAuthoring(entity);
                    if (chestAuthoring == null)
                        return;

                    // Handle particle activation/deactivation
                    // This would need access to the particle GameObject from ChestAuthoring
                    // For now, this is a placeholder

                }).WithoutBurst().Run();
        }

        private ChestAuthoring GetChestAuthoring(Entity entity)
        {
            // Try to get the authoring reference component
            if (EntityManager.HasComponent<ChestAuthoringReference>(entity))
            {
                var reference = EntityManager.GetComponentData<ChestAuthoringReference>(entity);
                return reference.AuthoringComponent.Value as ChestAuthoring;
            }

            return null;
        }
    }
}
