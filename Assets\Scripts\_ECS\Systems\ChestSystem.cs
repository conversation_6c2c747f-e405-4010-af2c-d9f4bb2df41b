using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using UnityEngine.UI;
using PlayerFAP.Components.Chest;
using PlayerFAP.Tags;
using Watermelon.SquadShooter;

namespace PlayerFAP.Systems.Chest
{
    /// <summary>
    /// Main system for handling chest opening logic, UI updates, and player interaction
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(TransformSystemGroup))]
    public partial struct ChestSystem : ISystem
    {
        private EntityQuery chestQuery;
        private EntityQuery playerQuery;
        private EntityQuery chestRewardRequestQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            // Query for all chest entities
            chestQuery = SystemAPI.QueryBuilder()
                .WithAll<ChestTag, ChestComponent, ChestOpeningComponent, LocalTransform>()
                .Build();

            // Query for player entities
            playerQuery = SystemAPI.QueryBuilder()
                .WithAll<PlayerTag, LocalTransform>()
                .Build();

            // Query for chest reward requests
            chestRewardRequestQuery = SystemAPI.QueryBuilder()
                .WithAll<ChestRewardRequest>()
                .Build();

            state.RequireForUpdate(chestQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            var deltaTime = SystemAPI.Time.DeltaTime;
            var currentTime = (float)SystemAPI.Time.ElapsedTime;

            // Get ECB for deferred operations
            var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Get player position if available
            float3 playerPosition = float3.zero;
            bool hasPlayer = false;
            if (!playerQuery.IsEmpty)
            {
                var playerEntity = playerQuery.GetSingletonEntity();
                var playerTransform = SystemAPI.GetComponent<LocalTransform>(playerEntity);
                playerPosition = playerTransform.Position;
                hasPlayer = true;
            }

            // Process chest interactions and opening logic
            var processChestsJob = new ProcessChestsJob
            {
                ECB = ecb.AsParallelWriter(),
                DeltaTime = deltaTime,
                CurrentTime = currentTime,
                PlayerPosition = playerPosition,
                HasPlayer = hasPlayer
            };

            state.Dependency = processChestsJob.ScheduleParallel(chestQuery, state.Dependency);

            // Process chest reward requests (create drop requests for DropPoolingSystem)
            if (!chestRewardRequestQuery.IsEmpty)
            {
                var processRewardsJob = new ProcessChestRewardsJob
                {
                    ECB = ecb.AsParallelWriter(),
                    CurrentTime = currentTime
                };

                state.Dependency = processRewardsJob.ScheduleParallel(chestRewardRequestQuery, state.Dependency);
            }
        }
    }

    /// <summary>
    /// Job to process chest interactions, opening logic, and UI updates
    /// </summary>
    [BurstCompile]
    public partial struct ProcessChestsJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float DeltaTime;
        [ReadOnly] public float CurrentTime;
        [ReadOnly] public float3 PlayerPosition;
        [ReadOnly] public bool HasPlayer;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref ChestComponent chest, ref ChestOpeningComponent opening, ref ChestUIComponent ui,
            in LocalTransform transform)
        {
            if (chest.IsOpened || !HasPlayer)
                return;

            // Calculate distance to player
            float distanceToPlayer = math.distance(transform.Position, PlayerPosition);
            bool playerNearby = distanceToPlayer <= chest.InteractionRange;

            // Update player nearby state
            bool wasPlayerNearby = chest.IsPlayerNearby;
            chest.IsPlayerNearby = playerNearby;

            // Handle player approach/leave events
            if (playerNearby && !wasPlayerNearby)
            {
                OnPlayerApproached(entity, entityInQueryIndex, ref chest, ref opening, ref ui);
            }
            else if (!playerNearby && wasPlayerNearby)
            {
                OnPlayerLeft(entity, entityInQueryIndex, ref chest, ref opening, ref ui);
            }

            // Process chest opening for InGame chests
            if (chest.ChestType == ChestType.InGame && playerNearby && !chest.IsOpened)
            {
                ProcessInGameChestOpening(entity, entityInQueryIndex, ref chest, ref opening, ref ui);
            }

            // Update UI scale animation
            UpdateUIAnimation(ref ui);
        }

        private void OnPlayerApproached(Entity entity, int entityInQueryIndex,
            ref ChestComponent chest, ref ChestOpeningComponent opening, ref ChestUIComponent ui)
        {
            // Add interaction tag
            ECB.AddComponent<ChestInteractionTag>(entityInQueryIndex, entity);

            // Trigger shake animation
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
            {
                CurrentAnimationState = 0,
                TriggerShake = true,
                TriggerOpen = false,
                TriggerIdle = false
            });

            // Start opening process for InGame chests
            if (chest.ChestType == ChestType.InGame)
            {
                opening.IsOpening = true;
                opening.StartTime = CurrentTime;
                opening.Progress = 0f;
                opening.OpeningCancelled = false;

                // Show UI
                ui.UIVisible = true;
                ui.UIScaleTarget = 1f;
            }
            // Show button for Rewarded chests
            else if (chest.ChestType == ChestType.Rewarded)
            {
                ECB.SetComponent(entityInQueryIndex, entity, new RewardedChestComponent
                {
                    ButtonEntity = Entity.Null,
                    AdWatched = false,
                    ButtonVisible = true
                });
            }
        }

        private void OnPlayerLeft(Entity entity, int entityInQueryIndex,
            ref ChestComponent chest, ref ChestOpeningComponent opening, ref ChestUIComponent ui)
        {
            // Remove interaction tag
            ECB.RemoveComponent<ChestInteractionTag>(entityInQueryIndex, entity);

            // Trigger idle animation
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
            {
                CurrentAnimationState = 0,
                TriggerShake = false,
                TriggerOpen = false,
                TriggerIdle = true
            });

            // Cancel opening for InGame chests
            if (chest.ChestType == ChestType.InGame && opening.IsOpening)
            {
                opening.IsOpening = false;
                opening.OpeningCancelled = true;
                opening.Progress = 0f;

                // Hide UI
                ui.UIVisible = false;
                ui.UIScaleTarget = 0f;
            }
            // Hide button for Rewarded chests
            else if (chest.ChestType == ChestType.Rewarded)
            {
                ECB.SetComponent(entityInQueryIndex, entity, new RewardedChestComponent
                {
                    ButtonEntity = Entity.Null,
                    AdWatched = false,
                    ButtonVisible = false
                });
            }
        }

        private void ProcessInGameChestOpening(Entity entity, int entityInQueryIndex,
            ref ChestComponent chest, ref ChestOpeningComponent opening, ref ChestUIComponent ui)
        {
            if (!opening.IsOpening)
                return;

            // Update progress
            float elapsed = CurrentTime - opening.StartTime;
            opening.Progress = math.clamp(elapsed / chest.OpenDuration, 0f, 1f);

            // Check if opening is complete
            if (opening.Progress >= 1f)
            {
                CompleteChestOpening(entity, entityInQueryIndex, ref chest, ref opening, ref ui);
            }
        }

        private void CompleteChestOpening(Entity entity, int entityInQueryIndex,
            ref ChestComponent chest, ref ChestOpeningComponent opening, ref ChestUIComponent ui)
        {
            // Mark chest as opened
            chest.IsOpened = true;
            opening.IsOpening = false;
            opening.Progress = 1f;

            // Hide UI
            ui.UIVisible = false;
            ui.UIScaleTarget = 0f;

            // Trigger open animation
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
            {
                CurrentAnimationState = 0,
                TriggerShake = false,
                TriggerOpen = true,
                TriggerIdle = false
            });

            // Deactivate particles
            ECB.SetComponent(entityInQueryIndex, entity, new ChestParticleComponent
            {
                ParticleEntity = Entity.Null,
                ParticleActive = false
            });

            // Play open sound
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAudioComponent
            {
                PlayOpenSound = true,
                PlayShakeSound = false,
                AudioVolume = 1f
            });

            // Create reward request
            CreateChestRewardRequest(entity, entityInQueryIndex, chest.ChestType == ChestType.Rewarded);
        }

        private void CreateChestRewardRequest(Entity chestEntity, int entityInQueryIndex, bool isRewarded)
        {
            var rewardRequestEntity = ECB.CreateEntity(entityInQueryIndex);
            ECB.AddComponent(entityInQueryIndex, rewardRequestEntity, new ChestRewardRequest
            {
                ChestEntity = chestEntity,
                SpawnPosition = PlayerPosition + new float3(0, 0, -1f), // Spawn in front of player
                SpawnRadius = 1.5f,
                IsRewarded = isRewarded,
                RequestTime = CurrentTime
            });
        }

        private void UpdateUIAnimation(ref ChestUIComponent ui)
        {
            // Simple lerp towards target scale
            float currentScale = ui.UIVisible ? 1f : 0f;
            // This would be handled by a separate UI system in a real implementation
            // For now, we just track the target
        }
    }

    /// <summary>
    /// Job to process chest reward requests and create drop requests for DropPoolingSystem
    /// </summary>
    [BurstCompile]
    public partial struct ProcessChestRewardsJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float CurrentTime;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            in ChestRewardRequest rewardRequest)
        {
            // Create a chest drop spawn request that will be processed by ChestDropSystem
            var chestDropEntity = ECB.CreateEntity(entityInQueryIndex);
            ECB.AddComponent(entityInQueryIndex, chestDropEntity, new ChestDropSpawnRequest
            {
                ChestEntity = rewardRequest.ChestEntity,
                SpawnPosition = rewardRequest.SpawnPosition,
                SpawnRadius = rewardRequest.SpawnRadius,
                IsRewarded = rewardRequest.IsRewarded,
                RequestTime = rewardRequest.RequestTime
            });

            // Remove the processed reward request
            ECB.DestroyEntity(entityInQueryIndex, entity);
        }
    }
}
