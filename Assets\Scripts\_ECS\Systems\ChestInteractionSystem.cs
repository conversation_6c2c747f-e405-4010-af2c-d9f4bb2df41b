using Unity.Burst;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using PlayerFAP.Components.Chest;
using PlayerFAP.Tags;
using Watermelon.SquadShooter;

namespace PlayerFAP.Systems.Chest
{
    /// <summary>
    /// System for detecting player interactions with chests
    /// This system handles approach/leave detection and triggers appropriate chest behaviors
    /// </summary>
    [BurstCompile]
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(ChestSystem))]
    public partial struct ChestInteractionSystem : ISystem
    {
        private EntityQuery chestQuery;
        private EntityQuery playerQuery;

        [BurstCompile]
        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<BeginSimulationEntityCommandBufferSystem.Singleton>();
            // Query for all chest entities
            chestQuery = SystemAPI.QueryBuilder()
                .WithAll<ChestTag, ChestComponent, LocalTransform>()
                .Build();

            // Query for player entities
            playerQuery = SystemAPI.QueryBuilder()
                .WithAll<PlayerAimTag, LocalTransform>()
                .Build();

            state.RequireForUpdate(chestQuery);
            state.RequireForUpdate(playerQuery);
        }

        [BurstCompile]
        public void OnUpdate(ref SystemState state)
        {
            // Get player position
            if (playerQuery.IsEmpty)
                return;

            var playerEntity = playerQuery.GetSingletonEntity();
            var playerTransform = SystemAPI.GetComponent<LocalTransform>(playerEntity);
            float3 playerPosition = playerTransform.Position;

            // Get ECB for deferred operations
            var ecbSingleton = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>();
            var ecb = ecbSingleton.CreateCommandBuffer(state.WorldUnmanaged);

            // Process chest interactions
            var interactionJob = new ChestInteractionJob
            {
                ECB = ecb.AsParallelWriter(),
                PlayerPosition = playerPosition,
                CurrentTime = (float)SystemAPI.Time.ElapsedTime
            };

            state.Dependency = interactionJob.ScheduleParallel(chestQuery, state.Dependency);
        }
    }

    /// <summary>
    /// Job to process chest-player interactions
    /// </summary>
    [BurstCompile]
    public partial struct ChestInteractionJob : IJobEntity
    {
        public EntityCommandBuffer.ParallelWriter ECB;
        [ReadOnly] public float3 PlayerPosition;
        [ReadOnly] public float CurrentTime;

        public void Execute(Entity entity, [EntityIndexInQuery] int entityInQueryIndex,
            ref ChestComponent chest, in LocalTransform transform)
        {
            // Skip if chest is already opened
            if (chest.IsOpened)
                return;

            // Calculate distance to player
            float distanceToPlayer = math.distance(transform.Position, PlayerPosition);
            bool playerNearby = distanceToPlayer <= chest.InteractionRange;

            // Check for state changes
            bool wasPlayerNearby = chest.IsPlayerNearby;
            chest.IsPlayerNearby = playerNearby;

            // Handle approach event
            if (playerNearby && !wasPlayerNearby)
            {
                OnPlayerApproached(entity, entityInQueryIndex, chest);
            }
            // Handle leave event
            else if (!playerNearby && wasPlayerNearby)
            {
                OnPlayerLeft(entity, entityInQueryIndex, chest);
            }
        }

        private void OnPlayerApproached(Entity entity, int entityInQueryIndex, ChestComponent chest)
        {
            // Add interaction tag to mark this chest as being interacted with
            ECB.AddComponent<ChestInteractionTag>(entityInQueryIndex, entity);

            // Trigger appropriate behavior based on chest type
            switch (chest.ChestType)
            {
                case ChestType.InGame:
                    HandleInGameChestApproach(entity, entityInQueryIndex);
                    break;
                case ChestType.Rewarded:
                    HandleRewardedChestApproach(entity, entityInQueryIndex);
                    break;
                case ChestType.Instant:
                    HandleInstantChestApproach(entity, entityInQueryIndex);
                    break;
            }

            // Trigger shake animation for all chest types
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
            {
                CurrentAnimationState = 0,
                TriggerShake = true,
                TriggerOpen = false,
                TriggerIdle = false
            });
        }

        private void OnPlayerLeft(Entity entity, int entityInQueryIndex, ChestComponent chest)
        {
            // Remove interaction tag
            ECB.RemoveComponent<ChestInteractionTag>(entityInQueryIndex, entity);

            // Trigger appropriate behavior based on chest type
            switch (chest.ChestType)
            {
                case ChestType.InGame:
                    HandleInGameChestLeave(entity, entityInQueryIndex);
                    break;
                case ChestType.Rewarded:
                    HandleRewardedChestLeave(entity, entityInQueryIndex);
                    break;
                case ChestType.Instant:
                    // Instant chests don't need leave handling
                    break;
            }

            // Trigger idle animation for all chest types (unless opened)
            if (!chest.IsOpened)
            {
                ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
                {
                    CurrentAnimationState = 0,
                    TriggerShake = false,
                    TriggerOpen = false,
                    TriggerIdle = true
                });
            }
        }

        private void HandleInGameChestApproach(Entity entity, int entityInQueryIndex)
        {
            // Start the opening process
            ECB.SetComponent(entityInQueryIndex, entity, new ChestOpeningComponent
            {
                StartTime = CurrentTime,
                Progress = 0f,
                IsOpening = true,
                OpeningCancelled = false
            });

            // Show UI
            ECB.SetComponent(entityInQueryIndex, entity, new ChestUIComponent
            {
                FillCircleEntity = Entity.Null,
                UIVisible = true,
                UIScaleTarget = 1f,
                UIScaleSpeed = 5f
            });
        }

        private void HandleInGameChestLeave(Entity entity, int entityInQueryIndex)
        {
            // Cancel the opening process
            ECB.SetComponent(entityInQueryIndex, entity, new ChestOpeningComponent
            {
                StartTime = 0f,
                Progress = 0f,
                IsOpening = false,
                OpeningCancelled = true
            });

            // Hide UI
            ECB.SetComponent(entityInQueryIndex, entity, new ChestUIComponent
            {
                FillCircleEntity = Entity.Null,
                UIVisible = false,
                UIScaleTarget = 0f,
                UIScaleSpeed = 5f
            });
        }

        private void HandleRewardedChestApproach(Entity entity, int entityInQueryIndex)
        {
            // Show the reward button
            ECB.SetComponent(entityInQueryIndex, entity, new RewardedChestComponent
            {
                ButtonEntity = Entity.Null,
                AdWatched = false,
                ButtonVisible = true
            });
        }

        private void HandleRewardedChestLeave(Entity entity, int entityInQueryIndex)
        {
            // Hide the reward button
            ECB.SetComponent(entityInQueryIndex, entity, new RewardedChestComponent
            {
                ButtonEntity = Entity.Null,
                AdWatched = false,
                ButtonVisible = false
            });
        }

        private void HandleInstantChestApproach(Entity entity, int entityInQueryIndex)
        {
            // Instantly open the chest
            ECB.SetComponent(entityInQueryIndex, entity, new ChestComponent
            {
                ChestType = ChestType.Instant,
                OpenDuration = 0f,
                IsOpened = true,
                IsPlayerNearby = true,
                InteractionRange = 2f,
                UIEntity = Entity.Null
            });

            // Trigger open animation immediately
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAnimationComponent
            {
                CurrentAnimationState = 0,
                TriggerShake = false,
                TriggerOpen = true,
                TriggerIdle = false
            });

            // Deactivate particles
            ECB.SetComponent(entityInQueryIndex, entity, new ChestParticleComponent
            {
                ParticleEntity = Entity.Null,
                ParticleActive = false
            });

            // Play open sound
            ECB.SetComponent(entityInQueryIndex, entity, new ChestAudioComponent
            {
                PlayOpenSound = true,
                PlayShakeSound = false,
                AudioVolume = 1f
            });

            // Create reward request immediately
            var rewardRequestEntity = ECB.CreateEntity(entityInQueryIndex);
            ECB.AddComponent(entityInQueryIndex, rewardRequestEntity, new ChestRewardRequest
            {
                ChestEntity = entity,
                SpawnPosition = PlayerPosition + new float3(0, 0, -1f),
                SpawnRadius = 1.5f,
                IsRewarded = false,
                RequestTime = CurrentTime
            });
        }
    }

    /// <summary>
    /// System for handling rewarded chest button interactions
    /// This system processes ad watching and chest opening for rewarded chests
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateAfter(typeof(ChestInteractionSystem))]
    public partial class RewardedChestButtonSystem : SystemBase
    {
        private EntityQuery rewardedChestQuery;

        protected override void OnCreate()
        {
            rewardedChestQuery = GetEntityQuery(
                ComponentType.ReadWrite<RewardedChestComponent>(),
                ComponentType.ReadWrite<ChestComponent>(),
                ComponentType.ReadOnly<ChestTag>()
            );

            RequireForUpdate(rewardedChestQuery);
        }

        protected override void OnUpdate()
        {
            var ecb = SystemAPI.GetSingleton<BeginSimulationEntityCommandBufferSystem.Singleton>()
                .CreateCommandBuffer(World.Unmanaged);

            var currentTime = (float)SystemAPI.Time.ElapsedTime;

            // This system would need to integrate with the existing AdsManager
            // For now, we'll simulate the ad watching process
            Entities
                .WithAll<ChestTag>()
                .ForEach((Entity entity, ref ChestComponent chest, ref RewardedChestComponent rewardedChest) =>
                {
                    // In a real implementation, this would be triggered by button clicks
                    // and integrate with AdsManager.ShowRewardBasedVideo
                    
                    // For demonstration purposes, we'll auto-trigger after a delay
                    // This should be replaced with actual button interaction logic
                    
                }).WithoutBurst().Run();
        }
    }
}
